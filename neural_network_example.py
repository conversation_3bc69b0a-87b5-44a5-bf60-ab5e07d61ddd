#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
神经网络奖励反馈示例
演示如何使用环境的奖励反馈机制来训练神经网络
"""

from main import WalkingEnvironment
import numpy as np
import time

class SimpleNeuralNetwork:
    """简单的神经网络示例（用于演示）"""
    
    def __init__(self, input_size=16, hidden_size=32, output_size=4):
        """初始化神经网络"""
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.output_size = output_size
        
        # 随机初始化权重
        self.W1 = np.random.randn(input_size, hidden_size) * 0.1
        self.b1 = np.zeros((1, hidden_size))
        self.W2 = np.random.randn(hidden_size, output_size) * 0.1
        self.b2 = np.zeros((1, output_size))
        
        # 学习率
        self.learning_rate = 0.001
        
        # 奖励反馈历史
        self.reward_feedback_history = []
        
    def forward(self, state):
        """前向传播"""
        # 确保输入是正确的形状
        if len(state.shape) == 1:
            state = state.reshape(1, -1)
            
        # 隐藏层
        z1 = np.dot(state, self.W1) + self.b1
        a1 = np.tanh(z1)  # 激活函数
        
        # 输出层
        z2 = np.dot(a1, self.W2) + self.b2
        action = np.tanh(z2)  # 输出动作范围 [-1, 1]
        
        return action.flatten()
    
    def process_reward_feedback(self, reward_feedback):
        """处理来自环境的奖励反馈"""
        if reward_feedback is not None:
            self.reward_feedback_history.append(reward_feedback)
            
            # 简单的奖励反馈处理：根据累积奖励调整学习率
            cumulative_reward = reward_feedback['cumulative_reward_10_steps']
            avg_reward = reward_feedback['avg_reward_10_steps']
            
            print(f"  🧠 神经网络收到奖励反馈:")
            print(f"     步数: {reward_feedback['feedback_step']}")
            print(f"     10步累积奖励: {cumulative_reward:.2f}")
            print(f"     10步平均奖励: {avg_reward:.2f}")
            
            # 根据奖励调整学习率（简单策略）
            if avg_reward > 0:
                self.learning_rate = min(0.01, self.learning_rate * 1.1)  # 增加学习率
                print(f"     ✅ 表现良好，学习率增加到: {self.learning_rate:.6f}")
            else:
                self.learning_rate = max(0.0001, self.learning_rate * 0.9)  # 降低学习率
                print(f"     ⚠️  表现不佳，学习率降低到: {self.learning_rate:.6f}")
    
    def get_action(self, state):
        """获取动作"""
        return self.forward(state)


def train_with_reward_feedback():
    """使用奖励反馈训练神经网络的示例"""
    print("🚀 开始神经网络奖励反馈训练示例")
    
    # 创建环境和神经网络
    env = WalkingEnvironment()
    neural_net = SimpleNeuralNetwork()
    
    # 设置奖励反馈间隔
    env.set_reward_feedback_interval(10)  # 每10步反馈一次
    
    try:
        episode = 0
        max_episodes = 5  # 运行5个回合作为示例
        
        while episode < max_episodes:
            print(f"\n=== 回合 {episode + 1} ===")
            
            # 重置环境
            state = env.reset()
            done = False
            step_count = 0
            episode_reward = 0
            
            while not done and step_count < 500:  # 最大500步
                # 神经网络生成动作
                action = neural_net.get_action(state)
                
                # 执行动作
                state, reward, done, info = env.step(action)
                episode_reward += reward
                step_count += 1
                
                # 处理奖励反馈
                reward_feedback = info.get('reward_feedback')
                neural_net.process_reward_feedback(reward_feedback)
                
                # 渲染（可选，注释掉以加快训练）
                # env.render()
                # env.clock.tick(60)
                
                # 每50步打印一次进度
                if step_count % 50 == 0:
                    print(f"  步数: {step_count}, 累积奖励: {episode_reward:.2f}, 位置: {info.get('torso_x', 0):.1f}")
            
            print(f"回合 {episode + 1} 结束:")
            print(f"  总步数: {step_count}")
            print(f"  总奖励: {episode_reward:.2f}")
            print(f"  最终位置: {info.get('torso_x', 0):.1f}")
            print(f"  神经网络收到 {len(neural_net.reward_feedback_history)} 次奖励反馈")
            
            episode += 1
            
            # 短暂暂停
            time.sleep(1)
        
        # 显示训练总结
        print(f"\n🎯 训练完成!")
        print(f"总回合数: {max_episodes}")
        print(f"神经网络总共收到 {len(neural_net.reward_feedback_history)} 次奖励反馈")
        
        # 显示神经网络反馈历史
        if neural_net.reward_feedback_history:
            print("\n📊 奖励反馈历史:")
            for i, feedback in enumerate(neural_net.reward_feedback_history[-5:]):  # 显示最后5次反馈
                print(f"  反馈 {i+1}: 步数 {feedback['feedback_step']}, "
                      f"累积奖励 {feedback['cumulative_reward_10_steps']:.2f}, "
                      f"平均奖励 {feedback['avg_reward_10_steps']:.2f}")
        
        # 显示环境的神经网络反馈信息
        nn_feedback_info = env.get_neural_network_feedback()
        print(f"\n🔧 环境反馈配置:")
        print(f"  反馈间隔: {nn_feedback_info['reward_feedback_interval']} 步")
        print(f"  总步数奖励: {nn_feedback_info['total_step_rewards']}")
        print(f"  最后反馈步数: {nn_feedback_info['last_feedback_step']}")
        
    except KeyboardInterrupt:
        print("\n⏹️  训练被用户中断")
    finally:
        env.close()


def interactive_feedback_demo():
    """交互式奖励反馈演示"""
    print("🎮 交互式神经网络奖励反馈演示")
    print("按空格键查看当前奖励反馈状态，按ESC退出")
    
    env = WalkingEnvironment()
    neural_net = SimpleNeuralNetwork()
    
    # 设置较短的反馈间隔用于演示
    env.set_reward_feedback_interval(5)  # 每5步反馈一次
    
    try:
        state = env.reset()
        running = True
        
        while running:
            # 处理事件
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        running = False
                    elif event.key == pygame.K_SPACE:
                        # 显示当前反馈状态
                        feedback_info = env.get_neural_network_feedback()
                        print(f"\n📋 当前反馈状态:")
                        print(f"  当前步数: {env.current_step}")
                        print(f"  距离下次反馈: {feedback_info['reward_feedback_interval'] - feedback_info['steps_since_last_feedback']} 步")
                        print(f"  最近10步奖励: {feedback_info['current_10_step_rewards']}")
                        print(f"  当前累积奖励: {feedback_info['current_cumulative_reward']:.2f}")
            
            # 神经网络生成动作
            action = neural_net.get_action(state)
            
            # 执行动作
            state, reward, done, info = env.step(action)
            
            # 处理奖励反馈
            reward_feedback = info.get('reward_feedback')
            neural_net.process_reward_feedback(reward_feedback)
            
            # 渲染
            env.render()
            env.clock.tick(60)
            
            # 如果回合结束，重置
            if done:
                state = env.reset()
                
    except KeyboardInterrupt:
        print("\n⏹️  演示被用户中断")
    finally:
        env.close()


if __name__ == "__main__":
    print("选择演示模式:")
    print("1. 自动训练演示")
    print("2. 交互式演示")
    
    choice = input("请输入选择 (1-2): ").strip()
    
    if choice == "1":
        train_with_reward_feedback()
    elif choice == "2":
        import pygame
        interactive_feedback_demo()
    else:
        print("无效选择，运行自动训练演示")
        train_with_reward_feedback()
